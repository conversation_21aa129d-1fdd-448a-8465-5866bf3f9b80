﻿# -*- coding: utf-8 -*-
import os
import subprocess
import sys
import json
import tempfile
import glob
import argparse
import re
from datetime import datetime
from pypinyin import pinyin, Style

# 日志相关的类和函数
class Logger:
    def __init__(self, log_file_path):
        self.log_file_path = log_file_path
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr

        # 确保logs目录存在
        os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

        # 打开日志文件
        self.log_file = open(log_file_path, 'w', encoding='utf-8')

    def write(self, text):
        # 同时写入控制台和日志文件
        self.original_stdout.write(text)
        self.original_stdout.flush()
        self.log_file.write(text)
        self.log_file.flush()

    def flush(self):
        self.original_stdout.flush()
        self.log_file.flush()

    def close(self):
        if hasattr(self, 'log_file') and self.log_file:
            self.log_file.close()

    def __enter__(self):
        sys.stdout = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout
        self.close()

def setup_logging():
    """设置日志记录"""
    # 创建logs目录
    logs_dir = "logs"
    os.makedirs(logs_dir, exist_ok=True)

    # 生成时间戳文件名
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file_path = os.path.join(logs_dir, f"main_{timestamp}.txt")

    return log_file_path

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def load_prompt_content(prompt_value):
    """
    加载prompt内容
    - 如果prompt_value以.md结尾，则从batch_configs/prompt目录读取对应的md文件
    - 否则直接返回prompt_value作为字符串内容

    Args:
        prompt_value: prompt参数值，可能是字符串内容或md文件名

    Returns:
        str: prompt的实际内容，如果读取失败则返回None
    """
    if prompt_value is None:
        return None

    # 检查是否为md文件名
    if isinstance(prompt_value, str) and prompt_value.endswith('.md'):
        # 构建文件路径
        prompt_file_path = os.path.join("batch_configs", "prompt", prompt_value)

        # 检查文件是否存在
        if not os.path.exists(prompt_file_path):
            print(f"警告：指定的prompt文件不存在: {prompt_file_path}")
            return None

        try:
            # 读取md文件内容
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            print(f"已从文件加载prompt: {prompt_file_path}")
            return content
        except Exception as e:
            print(f"错误：无法读取prompt文件 {prompt_file_path}: {e}")
            return None
    else:
        # 直接返回字符串内容
        return prompt_value

def create_config_copy_with_text_prompts(config_file_path, config_data, batch_results=None):
    """
    创建配置文件的副本，将md格式的prompt转换为文本内容，并添加准确率信息

    Args:
        config_file_path: 原配置文件路径
        config_data: 配置数据字典
        batch_results: 批处理结果列表，包含准确率信息

    Returns:
        str: 副本文件路径，如果创建失败则返回None
    """
    try:
        # 确保batch_configs_copy目录存在
        copy_dir = os.path.join("batch_configs", "batch_configs_copy")
        if not os.path.exists(copy_dir):
            os.makedirs(copy_dir)
            print(f"创建目录: {copy_dir}")

        # 生成副本文件名（添加时间戳）
        original_filename = os.path.basename(config_file_path)
        name_without_ext = os.path.splitext(original_filename)[0]
        # 生成时间戳，格式为 YYYY-MM-DD_HH-MM-SS
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        copy_filename = f"{name_without_ext}_copy_{timestamp}.json"
        copy_file_path = os.path.join(copy_dir, copy_filename)

        # 深拷贝配置数据
        import copy
        copy_config_data = copy.deepcopy(config_data)

        # 遍历配置，处理md转换和准确率添加
        prompt_fields = ["test_prompt", "test2_prompt", "test3_prompt", "one_stage_test_prompt"]
        has_changes = False

        for i, batch_config in enumerate(copy_config_data.get("batch_configs", [])):
            # 转换md格式的prompt
            for field in prompt_fields:
                if field in batch_config:
                    prompt_value = batch_config[field]
                    if isinstance(prompt_value, str) and prompt_value.endswith('.md'):
                        # 读取md文件内容并替换
                        text_content = load_prompt_content(prompt_value)
                        if text_content:
                            batch_config[field] = text_content
                            has_changes = True
                            print(f"  转换 {field}: {prompt_value} -> 文本内容")
                        else:
                            print(f"  警告：无法读取 {field} 的md文件: {prompt_value}")

            # 添加准确率信息
            if batch_results and i < len(batch_results):
                result = batch_results[i]
                if result['success']:
                    # 根据处理模式选择对应的准确率信息
                    processing_mode = batch_config.get("处理模式", 1)
                    if processing_mode == 1 and "accuracy_info" in result:
                        accuracy_str = result["accuracy_info"].get("accuracy_str", "未知")
                        batch_config["准确率"] = accuracy_str
                        has_changes = True
                    elif processing_mode == 2 and "test2_accuracy_info" in result:
                        accuracy_str = result["test2_accuracy_info"].get("accuracy_str", "未知")
                        batch_config["准确率"] = accuracy_str
                        has_changes = True
                    elif processing_mode == 3 and "test3_accuracy_info" in result:
                        accuracy_str = result["test3_accuracy_info"].get("accuracy_str", "未知")
                        batch_config["准确率"] = accuracy_str
                        has_changes = True

        # 总是创建副本文件，无论是否有更改
        with open(copy_file_path, 'w', encoding='utf-8') as f:
            json.dump(copy_config_data, f, ensure_ascii=False, indent=2)

        if has_changes:
            print(f"已创建配置副本（包含更新）: {copy_file_path}")
        else:
            print(f"已创建配置副本: {copy_file_path}")

        return copy_file_path

    except Exception as e:
        print(f"错误：创建配置副本失败: {e}")
        return None

def parse_command_line_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='AI图像识别验证工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python main.py                           # 交互式模式
  python main.py --name 1.json            # 指定配置文件运行
  python main.py --name 2025-08-01_example.json  # 指定配置文件运行
  python main.py --rename                  # 重命名配置文件为数字格式
  python main.py --list                    # 列出所有可用的配置文件
        '''
    )

    parser.add_argument(
        '--name',
        type=str,
        help='指定要使用的batch_configs配置文件名（如：1.json 或 2025-08-01_example.json）'
    )

    parser.add_argument(
        '--rename',
        action='store_true',
        help='将batch_configs目录中的所有JSON文件重命名为数字格式（1.json, 2.json, ...）'
    )

    parser.add_argument(
        '--list',
        action='store_true',
        help='列出batch_configs目录中所有可用的配置文件'
    )

    return parser.parse_args()

def rename_batch_config_files():
    """将batch_configs目录中的JSON文件重命名为数字格式（跳过example.json）"""
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return False

    # 获取所有JSON文件（排除子目录和example.json）
    json_files = []
    excluded_files = ["example.json"]  # 要跳过的文件列表

    for file in os.listdir(batch_configs_dir):
        file_path = os.path.join(batch_configs_dir, file)
        if os.path.isfile(file_path) and file.endswith('.json'):
            # 跳过指定的文件
            if file in excluded_files:
                print(f"  跳过 {file} (配置示例文件)")
                continue
            json_files.append(file)

    if not json_files:
        print(f"在 {batch_configs_dir} 中没有找到可重命名的JSON文件")
        return False

    # 按文件名排序
    json_files.sort()

    print(f"找到 {len(json_files)} 个可重命名的JSON文件，开始重命名...")

    # 重命名文件
    renamed_count = 0
    for i, old_filename in enumerate(json_files, 1):
        new_filename = f"{i}.json"
        old_path = os.path.join(batch_configs_dir, old_filename)
        new_path = os.path.join(batch_configs_dir, new_filename)

        # 如果新文件名已经存在且不是当前文件，跳过
        if os.path.exists(new_path) and old_filename != new_filename:
            print(f"  跳过 {old_filename} -> {new_filename} (目标文件已存在)")
            continue

        # 如果文件名已经是数字格式，跳过
        if old_filename == new_filename:
            print(f"  跳过 {old_filename} (已是数字格式)")
            continue

        try:
            os.rename(old_path, new_path)
            print(f"  ✓ {old_filename} -> {new_filename}")
            renamed_count += 1
        except Exception as e:
            print(f"  ✗ 重命名失败 {old_filename} -> {new_filename}: {e}")

    print(f"重命名完成，共重命名 {renamed_count} 个文件")
    return True

def get_input_mode_choice():
    """让用户选择输入模式：手动输入或batch_configs读取"""
    print("请选择输入模式：")
    print("1. 手动输入")
    print("2. batch_configs读取")

    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def apply_default_values(config):
    """为配置项应用默认值（题型字段除外）"""
    # 定义默认值
    defaults = {
        "处理模式": 1,  # 1=单阶段，2=双阶段不发图片，3=双阶段发图片
        "round2批改模式": 2,  # 默认使用JSON比对
        "模型ID": 1,
        "response_format": 1,  # 默认使用text格式，1=text，2=json_object
        "图像文件夹": 1,
        "像素增强": "n",
        "像素粘连": "n",
        "图像放大倍数": 1
        # 注意：灰度阀门不设置默认值，因为只有在batch_configs中明确指定时才使用
    }

    # 应用默认值，但不覆盖已存在的值
    for key, default_value in defaults.items():
        if key not in config:
            config[key] = default_value
            print(f"  应用默认值: {key} = {default_value}")

    # 检查题型字段是否存在
    if "题型" not in config:
        print(f"  警告：配置中缺少必需的'题型'字段")
        return False

    return True

def get_batch_config(config_filename=None):
    """
    获取batch_configs配置文件

    Args:
        config_filename: 指定的配置文件名，如果为None则获取按数字排序的最小文件

    Returns:
        dict: 配置数据，如果失败则返回None
    """
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return None

    if config_filename:
        # 指定了文件名
        config_file_path = os.path.join(batch_configs_dir, config_filename)
        if not os.path.exists(config_file_path):
            print(f"错误：指定的配置文件不存在: {config_file_path}")
            return None
        target_file = config_file_path
        print(f"使用指定的配置文件：{config_filename}")
    else:
        # 未指定文件名，按数字排序选择最小的（排除example.json）
        json_files = []
        excluded_files = ["example.json"]  # 要排除的文件列表

        for file in os.listdir(batch_configs_dir):
            file_path = os.path.join(batch_configs_dir, file)
            if os.path.isfile(file_path) and file.endswith('.json'):
                # 跳过指定的文件
                if file in excluded_files:
                    continue
                json_files.append(file)

        if not json_files:
            print(f"错误：{batch_configs_dir} 文件夹中没有找到可用的JSON配置文件")
            return None

        # 按数字排序（优先数字文件名，然后按字母排序）
        def sort_key(filename):
            # 提取文件名中的数字（如果是纯数字.json格式）
            name_without_ext = filename[:-5]  # 去掉.json
            if name_without_ext.isdigit():
                return (0, -int(name_without_ext))  # 数字文件优先，按数字大小倒序排序（负号实现倒序）
            else:
                return (1, filename)  # 非数字文件按字母排序

        json_files.sort(key=sort_key)
        target_file = os.path.join(batch_configs_dir, json_files[0])
        print(f"自动选择配置文件：{json_files[0]}")

    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        print(f"已加载配置文件：{target_file}")

        # 处理batch_configs中的每个配置项，应用默认值
        batch_configs = config_data.get("batch_configs", [])
        valid_configs = []

        for i, config in enumerate(batch_configs):
            print(f"\n处理第 {i+1} 个配置:")
            if apply_default_values(config):
                valid_configs.append(config)
                print(f"  ✓ 配置 {i+1} 验证通过")
            else:
                print(f"  ✗ 配置 {i+1} 验证失败，跳过此配置")

        # 更新配置数据
        config_data["batch_configs"] = valid_configs
        print(f"\n有效配置数量: {len(valid_configs)}/{len(batch_configs)}")

        return config_data
    except Exception as e:
        print(f"错误：无法读取配置文件 {target_file}: {e}")
        return None

def get_latest_batch_config():
    """获取batch_configs文件夹中时间最晚的JSON文件（兼容性函数）"""
    return get_batch_config()

def list_available_configs():
    """列出可用的配置文件（排除example.json）"""
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return []

    json_files = []
    excluded_files = ["example.json"]  # 要排除的文件列表

    for file in os.listdir(batch_configs_dir):
        file_path = os.path.join(batch_configs_dir, file)
        if os.path.isfile(file_path) and file.endswith('.json'):
            # 跳过指定的文件
            if file in excluded_files:
                continue
            json_files.append(file)

    # 按数字排序
    def sort_key(filename):
        name_without_ext = filename[:-5]  # 去掉.json
        if name_without_ext.isdigit():
            return (0, -int(name_without_ext))  # 负号实现倒序排序，数字大的在前
        else:
            return (1, filename)

    json_files.sort(key=sort_key)
    return json_files

def get_processing_mode_choice():
    """让用户选择处理模式"""
    print("请选择处理模式：")
    print("1. 单阶段处理（调用one_stage_test）")
    print("2. 双阶段不发图片（调用test+test2）")
    print("3. 双阶段发图片（调用test+test3）")

    while True:
        choice = input("请输入选择（1、2或3）：").strip()
        if choice in ['1', '2', '3']:
            return choice
        else:
            print("输入无效，请输入1、2或3")

def get_round2_grading_mode():
    """让用户选择round2批改模式"""
    print("请选择round2批改模式：")
    print("1. 使用大模型批改")
    print("2. 使用JSON比对")

    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def run_script(script_name, args=None):
    """运行指定的Python脚本"""
    try:
        print(f"\n正在运行 {script_name}...")
        print("=" * 50)

        # 构建命令
        cmd = [sys.executable, script_name]
        if args:
            cmd.extend(args)

        # 不捕获输出，让子进程直接与用户交互
        result = subprocess.run(cmd)

        print("=" * 50)
        if result.returncode == 0:
            print(f"✓ {script_name} 运行成功")
            return True
        else:
            print(f"✗ {script_name} 运行失败，返回码: {result.returncode}")
            return False

    except Exception as e:
        print(f"✗ 运行 {script_name} 时发生异常: {str(e)}")
        return False

def save_config(config_data):
    """保存配置到临时文件"""
    config_file = "temp_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    return config_file

def load_config():
    """从临时文件加载配置"""
    config_file = "temp_config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def cleanup_config():
    """清理临时配置文件"""
    config_file = "temp_config.json"
    if os.path.exists(config_file):
        try:
            os.remove(config_file)
        except:
            pass

def print_batch_summary(batch_results):
    """打印批处理执行总结"""
    print("\n" + "="*60)
    print("批处理执行总结")
    print("="*60)

    # 题型映射
    question_types = {
        1: "涂卡选择题",
        2: "涂卡判断题",
        3: "连线题",
        4: "图表题",
        5: "翻译题",
        6: "画图题",
        7: "数学应用题",
        8: "数学计算题",
        9: "简单的四则运算",
        10: "填空题",
        11: "判断题",
        12: "多选题",
        13: "单选题"
    }

    # 模型映射
    model_mapping = {
        1: "doubao-seed-1-6-250615",
        2: "doubao-seed-1-6-flash-250715",
        3: "doubao-1-5-thinking-vision-pro-250428",
        4: "doubao-1-5-vision-pro-32k-250115"
    }

    failed_batches = []  # 记录失败的批处理

    for result in batch_results:
        batch_index = result['batch_index']
        config = result['config']
        success = result['success']

        # 获取题型和模型信息
        question_type_num = config.get('题型', 1)
        model_id_num = config.get('模型ID', 1)
        question_type = question_types.get(question_type_num, "未知题型")
        model_name = model_mapping.get(model_id_num, "未知模型")

        print(f"第 {batch_index} 次批处理")
        print(f"题型：{question_type}")
        print(f"模型：{model_name}")

        # 检查是否有循环结果信息
        if 'loop_results' in result.get('accuracy_info', {}) or \
           'loop_results' in result.get('test2_accuracy_info', {}) or \
           'loop_results' in result.get('test3_accuracy_info', {}):
            # 从准确率信息中获取循环次数
            processing_mode = config.get("处理模式", 1)
            accuracy_info = None
            if processing_mode == 1:
                accuracy_info = result.get('accuracy_info', {})
            elif processing_mode == 2:
                accuracy_info = result.get('test2_accuracy_info', {})
            elif processing_mode == 3:
                accuracy_info = result.get('test3_accuracy_info', {})

            if accuracy_info and 'loop_results' in accuracy_info:
                loop_count = len(accuracy_info['loop_results'])
                print(f"循环次数：{loop_count}")


        if success:
            # 根据处理模式输出不同的准确率信息
            processing_mode = config.get('处理模式', 1)

            if processing_mode == 1:
                # 单阶段模式
                accuracy_info = result.get('accuracy_info', {})
                accuracy_str = accuracy_info.get('accuracy_str', '准确率: 未知')
                print(f"one_stage_test {accuracy_str}")

            elif processing_mode == 2:
                # 双阶段不发图片模式
                test_accuracy_info = result.get('test_accuracy_info', {})
                test2_accuracy_info = result.get('test2_accuracy_info', {})

                test_accuracy_str = test_accuracy_info.get('accuracy_str', '准确率: 未知')
                test2_accuracy_str = test2_accuracy_info.get('accuracy_str', '准确率: 未知')

                print(f"test {test_accuracy_str}")
                print(f"test2 {test2_accuracy_str}")

            elif processing_mode == 3:
                # 双阶段发图片模式
                test_accuracy_info = result.get('test_accuracy_info', {})
                test3_accuracy_info = result.get('test3_accuracy_info', {})

                test_accuracy_str = test_accuracy_info.get('accuracy_str', '准确率: 未知')
                test3_accuracy_str = test3_accuracy_info.get('accuracy_str', '准确率: 未知')

                print(f"test {test_accuracy_str}")
                print(f"test3 {test3_accuracy_str}")
        else:
            # 处理失败
            error_message = result.get('error_message', '未知错误')
            print(f"处理失败：{error_message}")
            failed_batches.append({
                'batch_index': batch_index,
                'question_type': question_type,
                'model_name': model_name,
                'error_message': error_message
            })

        print()  # 空行分隔

    # 输出失败的批处理总结
    if failed_batches:
        print("="*60)
        print("失败的批处理总结")
        print("="*60)
        for failed in failed_batches:
            print(f"第 {failed['batch_index']} 次批处理失败")
            print(f"题型：{failed['question_type']}")
            print(f"模型：{failed['model_name']}")
            print(f"错误信息：{failed['error_message']}")
            print()
    else:
        print("="*60)
        print("所有批处理均执行成功！")
        print("="*60)

def run_single_batch_config_once(config, batch_index, loop_index=0):
    """运行单个批处理配置一次，返回执行结果"""
    # 开始执行批处理配置（不打印详细信息）

    # 从配置中提取参数（现在应该都有默认值了）
    # 处理新旧格式的兼容性
    processing_mode = config.get("处理模式", 1)

    # 如果存在旧格式的"round2图片"参数，则转换为新格式
    if "round2图片" in config:
        stage_choice = config.get("处理模式", 1)
        round2_choice = config.get("round2图片", 1)

        if stage_choice == 1:
            processing_mode = 1  # 单阶段
        elif stage_choice == 2:
            if round2_choice == 1:
                processing_mode = 3  # 双阶段发图片
            else:
                processing_mode = 2  # 双阶段不发图片
        print(f"检测到旧格式配置，已转换：处理模式={stage_choice}, round2图片={round2_choice} -> 新处理模式={processing_mode}")

    round2_grading_mode = str(config.get("round2批改模式", 2))  # 1=大模型批改，2=JSON比对
    model_id_num = config.get("模型ID", 1)
    response_format_num = config.get("response_format", 1)  # 1=text，2=json_object
    question_type_num = config.get("题型")  # 题型是必需字段，不设默认值
    images_dir_num = config.get("图像文件夹", 1)
    use_enhance = config.get("像素增强", "n") == "y"  # 默认值改为"n"
    use_pixel_connection = config.get("像素粘连", "n") == "y"  # "y"=采用，"n"=不采用，默认为"n"
    scale = config.get("图像放大倍数", 1)  # 默认值改为1

    # 处理灰度阀门参数
    # 如果像素增强为"n"，则直接忽略灰度阀门参数
    gray_threshold = None
    if use_enhance:  # 只有在像素增强为"y"时才考虑灰度阀门
        if "灰度阀门" in config:
            gray_threshold = config.get("灰度阀门")
            # 验证灰度阀门值是否在有效范围内
            if not isinstance(gray_threshold, (int, float)) or not (0 <= gray_threshold <= 255):
                print(f"警告：灰度阀门值 {gray_threshold} 无效，必须在0-255范围内，将使用默认值200")
                gray_threshold = 200
            else:
                print(f"使用batch_configs中的灰度阀门值: {gray_threshold}")
        else:
            # 如果没有灰度阀门参数，使用默认值200
            gray_threshold = 200
            print(f"未指定灰度阀门参数，使用默认值: {gray_threshold}")
    else:
        print("像素增强为'n'，忽略灰度阀门参数")

    # 提取自定义prompt参数
    # 支持两种格式：
    # 1. 直接字符串内容
    # 2. 以.md结尾的文件名，从batch_configs/prompt目录读取
    test_prompt = load_prompt_content(config.get("test_prompt"))
    test2_prompt = load_prompt_content(config.get("test2_prompt"))
    test3_prompt = load_prompt_content(config.get("test3_prompt"))
    one_stage_test_prompt = load_prompt_content(config.get("one_stage_test_prompt"))

    # 提取API参数（temperature、top_p、max_tokens）
    temperature = config.get("temperature")
    top_p = config.get("top_p")
    max_tokens = config.get("max_tokens")

    # 检查必需字段
    if question_type_num is None:
        print(f"错误：配置中缺少必需的'题型'字段")
        if processing_mode == 1:
            stage_mode = '单阶段'
            round2_mode = None
        elif processing_mode == 2:
            stage_mode = '双阶段'
            round2_mode = '不发图片'
        elif processing_mode == 3:
            stage_mode = '双阶段'
            round2_mode = '发图片'
        else:
            stage_mode = '未知'
            round2_mode = None

        result = {
            'batch_index': batch_index + 1,
            'loop_index': loop_index + 1,
            'config': config,
            'success': False,
            'stage_mode': stage_mode,
            'round2_mode': round2_mode,
            'output_files': [],
            'error_message': "配置中缺少必需的'题型'字段"
        }
        return result

    # 不打印详细的配置参数信息

    # 初始化结果对象
    if processing_mode == 1:
        stage_mode = '单阶段'
        round2_mode = None
    elif processing_mode == 2:
        stage_mode = '双阶段'
        round2_mode = '不发图片'
    elif processing_mode == 3:
        stage_mode = '双阶段'
        round2_mode = '发图片'
    else:
        stage_mode = '未知'
        round2_mode = None

    result = {
        'batch_index': batch_index + 1,
        'loop_index': loop_index + 1,
        'config': config,
        'success': False,
        'stage_mode': stage_mode,
        'round2_mode': round2_mode,
        'output_files': [],
        'error_message': None
    }

    # 映射模型ID - 转换为模型字符串
    model_mapping = {
        1: "doubao-seed-1-6-250615",
        2: "doubao-seed-1-6-flash-250715",
        3: "doubao-1-5-thinking-vision-pro-250428",
        4: "doubao-1-5-vision-pro-32k-250115"
    }
    model_id = model_mapping.get(model_id_num, "doubao-seed-1-6-flash-250715")

    # 映射response_format - 转换为response_format字符串
    response_format_mapping = {
        1: "text",
        2: "json_object"
    }
    response_format = response_format_mapping.get(response_format_num, "text")

    # 映射题型 - 转换为题型字符串和拼音
    question_types = {
        1: "涂卡选择题",
        2: "涂卡判断题",
        3: "连线题",
        4: "图表题",
        5: "翻译题",
        6: "画图题",
        7: "数学应用题",
        8: "数学计算题",
        9: "简单的四则运算",
        10: "填空题",
        11: "判断题",
        12: "多选题",
        13: "单选题"
    }
    question_type = question_types.get(question_type_num, "涂卡选择题")
    pinyin_name = chinese_to_pinyin(question_type)

    # 映射图像文件夹 - 转换为文件夹名称
    folder_mapping = {
        1: "images",
        2: "OpenCV_result",
        3: "grounding_result",
        4: "YOLO_result",
        5: "YOLO_text_result",
        6: "manual_result",
        7: "roboflow_yolo_result",
        8: "pixel_enhancement_result_java",
        9: "pixel_enhancement_result_python"
    }
    images_dir_name = folder_mapping.get(images_dir_num, "images")
    
    # 特殊处理pixel_enhancement_result文件夹
    if images_dir_num == 8:
        # 当选择图像文件夹编号8时，自动使用java_process_images子文件夹
        images_dir_name = "pixel_enhancement_result/java_process_images"
    elif images_dir_num == 9:
        # 当选择图像文件夹编号9时，自动使用python_process_images子文件夹
        images_dir_name = "pixel_enhancement_result/python_process_images"

    if processing_mode == 1:
        # 单阶段模式

        # 导入one_stage_test模块并调用其函数
        try:
            import one_stage_test
            success, accuracy_info = one_stage_test.run_one_stage_test(
                model_id=model_id,
                response_format=response_format,
                question_type=question_type,
                pinyin_name=pinyin_name,
                images_dir=images_dir_name,
                use_enhance=use_enhance,
                scale=scale,
                use_pixel_connection=use_pixel_connection,
                custom_prompt=one_stage_test_prompt,
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens,
                gray_threshold=gray_threshold  # 传递灰度阀门参数
            )
            if success:
                result['success'] = True
                result['accuracy_info'] = accuracy_info

                # 添加输出文件路径
                types_dir = "types"
                question_dir = os.path.join(types_dir, pinyin_name)
                one_stage_response_dir = os.path.join(question_dir, "one_stage_response")
                result['output_files'].append({
                    'type': 'one_stage_response',
                    'directory': one_stage_response_dir,
                    'description': '单阶段处理结果'
                })
            else:
                result['error_message'] = "单阶段处理返回失败状态"
        except Exception as e:
            result['error_message'] = str(e)

    elif processing_mode in [2, 3]:
        # 双阶段模式

        try:
            import test

            # 运行test.py
            success1, test_accuracy_info = test.run_test(
                model_id=model_id,
                response_format=response_format,
                question_type=question_type,
                pinyin_name=pinyin_name,
                images_dir=images_dir_name,
                use_enhance=use_enhance,
                scale=scale,
                use_pixel_connection=use_pixel_connection,
                custom_prompt=test_prompt,
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens,
                gray_threshold=gray_threshold  # 传递灰度阀门参数
            )

            if not success1:
                result['error_message'] = "第一阶段test.py运行失败"
                return result

            # 保存配置供第二阶段使用
            types_dir = "types"
            question_dir = os.path.join(types_dir, pinyin_name)
            config_data = {
                'model_id': model_id,
                'response_format': response_format,
                'question_type': question_type,
                'pinyin_name': pinyin_name,
                'images_dir': images_dir_name,
                'question_dir': question_dir,
                'use_enhance': use_enhance,
                'scale': scale,
                'use_pixel_connection': use_pixel_connection,
                'grading_mode': 'model' if round2_grading_mode == '1' else 'json_compare',  # 添加批改模式
                'test2_prompt': test2_prompt,  # 添加test2自定义prompt
                'test3_prompt': test3_prompt,   # 添加test3自定义prompt
                'temperature': temperature,  # 添加temperature参数
                'top_p': top_p,  # 添加top_p参数
                'max_tokens': max_tokens,  # 添加max_tokens参数
                'gray_threshold': gray_threshold  # 添加灰度阀门参数
            }
            config_file = save_config(config_data)

            # 添加第一阶段输出文件
            response_dir = os.path.join(question_dir, "response")
            result['output_files'].append({
                'type': 'response',
                'directory': response_dir,
                'description': '第一阶段处理结果'
            })

            if processing_mode == 3:
                # 双阶段发图片：调用test3.py
                try:
                    import test3
                    success2, test3_accuracy_info = test3.run_test3_with_config(config_file)
                    if success2:
                        result['success'] = True
                        result['test_accuracy_info'] = test_accuracy_info
                        result['test3_accuracy_info'] = test3_accuracy_info

                        # 添加第二阶段输出文件
                        round2_response_with_images_dir = os.path.join(question_dir, "round2_response_with_images")
                        result['output_files'].append({
                            'type': 'round2_response_with_images',
                            'directory': round2_response_with_images_dir,
                            'description': '第二阶段处理结果（发图片）'
                        })
                    else:
                        result['error_message'] = "第二阶段test3.py运行失败"
                except Exception as e:
                    result['error_message'] = f"第二阶段test3.py执行异常: {e}"
            else:  # processing_mode == 2
                # 双阶段不发图片：调用test2.py
                try:
                    import test2
                    success2, test2_accuracy_info = test2.run_test2_with_config(config_file)
                    if success2:
                        result['success'] = True
                        result['test_accuracy_info'] = test_accuracy_info
                        result['test2_accuracy_info'] = test2_accuracy_info

                        # 添加第二阶段输出文件
                        round2_response_without_images_dir = os.path.join(question_dir, "round2_response_without_images")
                        result['output_files'].append({
                            'type': 'round2_response_without_images',
                            'directory': round2_response_without_images_dir,
                            'description': '第二阶段处理结果（不发图片）'
                        })
                    else:
                        result['error_message'] = "第二阶段test2.py运行失败"
                except Exception as e:
                    result['error_message'] = f"第二阶段test2.py执行异常: {e}"

            # 清理配置文件
            cleanup_config()

        except Exception as e:
            result['error_message'] = str(e)
            cleanup_config()

    return result

def run_single_batch_config(config, batch_index, loop_count=1):
    """运行单个批处理配置，支持循环次数，返回执行结果"""
    # 验证循环次数
    if not isinstance(loop_count, int) or loop_count < 1:
        print(f"警告：循环次数 {loop_count} 无效，必须是大于等于1的整数，将使用默认值1")
        loop_count = 1

    if loop_count == 1:
        # 单次运行，保持原有逻辑
        return run_single_batch_config_once(config, batch_index, 0)
    else:
        # 多次运行，收集所有结果
        print(f"配置 {batch_index + 1} 将运行 {loop_count} 次")

        all_results = []
        accuracy_values = []  # 存储每轮的准确率数值

        for loop_index in range(loop_count):
            print(f"  第 {loop_index + 1} 轮运行...")
            result = run_single_batch_config_once(config, batch_index, loop_index)
            all_results.append(result)

            # 提取准确率数值
            if result['success']:
                processing_mode = config.get("处理模式", 1)
                accuracy = None

                if processing_mode == 1 and "accuracy_info" in result:
                    accuracy = result["accuracy_info"].get("accuracy", 0.0)
                elif processing_mode == 2 and "test2_accuracy_info" in result:
                    accuracy = result["test2_accuracy_info"].get("accuracy", 0.0)
                elif processing_mode == 3 and "test3_accuracy_info" in result:
                    accuracy = result["test3_accuracy_info"].get("accuracy", 0.0)

                if accuracy is not None:
                    accuracy_values.append(accuracy)
                    print(f"    第 {loop_index + 1} 轮准确率: {(accuracy*100):.2f}%")

        # 计算平均准确率
        if accuracy_values:
            avg_accuracy = sum(accuracy_values) / len(accuracy_values)
            print(f"  平均准确率: {(avg_accuracy*100):.2f}%")

            # 构建综合准确率字符串
            accuracy_parts = [f"准确率平均值: {(avg_accuracy*100):.2f}%"]
            for i, acc in enumerate(accuracy_values):
                accuracy_parts.append(f"第{i+1}轮准确率: {(acc*100):.2f}%")
            combined_accuracy_str = ", ".join(accuracy_parts)

            # 创建综合结果，基于第一个成功的结果
            successful_result = None
            for result in all_results:
                if result['success']:
                    successful_result = result
                    break

            if successful_result:
                # 更新准确率信息
                processing_mode = config.get("处理模式", 1)
                combined_accuracy_info = {
                    'accuracy': avg_accuracy,
                    'accuracy_str': combined_accuracy_str,
                    'total': successful_result.get('accuracy_info', {}).get('total', 0) if processing_mode == 1 else
                            successful_result.get('test2_accuracy_info', {}).get('total', 0) if processing_mode == 2 else
                            successful_result.get('test3_accuracy_info', {}).get('total', 0),
                    'wrong_count': 0,  # 多轮运行时不统计错题数量
                    'accuracy_percentage': f"{(avg_accuracy*100):.2f}%",
                    'loop_results': all_results  # 保存所有轮次的详细结果
                }

                # 创建综合结果
                combined_result = successful_result.copy()
                combined_result['loop_index'] = 0  # 综合结果的loop_index设为0

                if processing_mode == 1:
                    combined_result['accuracy_info'] = combined_accuracy_info
                elif processing_mode == 2:
                    combined_result['test2_accuracy_info'] = combined_accuracy_info
                elif processing_mode == 3:
                    combined_result['test3_accuracy_info'] = combined_accuracy_info

                return combined_result

        # 如果没有成功的结果，返回最后一个结果
        return all_results[-1] if all_results else {
            'batch_index': batch_index + 1,
            'loop_index': 0,
            'config': config,
            'success': False,
            'error_message': "所有循环都失败了"
        }

def main():
    """主函数：控制整个流程"""

    # 解析命令行参数
    args = parse_command_line_args()

    # 处理列出配置文件的命令
    if args.list:
        print("\n=== 可用的配置文件 ===")
        available_configs = list_available_configs()
        if available_configs:
            for i, config_file in enumerate(available_configs, 1):
                print(f"  {i}. {config_file}")
            print(f"\n总共找到 {len(available_configs)} 个配置文件")
        else:
            print("没有找到任何配置文件")
        return

    # 处理重命名命令
    if args.rename:
        print("\n=== 重命名配置文件 ===")
        available_configs = list_available_configs()
        if available_configs:
            print("重命名前的配置文件:")
            for i, config_file in enumerate(available_configs, 1):
                print(f"  {i}. {config_file}")

            non_numeric_files = [f for f in available_configs if not f[:-5].isdigit()]
            if non_numeric_files:
                print(f"\n检测到 {len(non_numeric_files)} 个非数字命名的配置文件")
                print("开始重命名...")
                if rename_batch_config_files():
                    print("\n重命名后的配置文件:")
                    new_configs = list_available_configs()
                    for i, config_file in enumerate(new_configs, 1):
                        print(f"  {i}. {config_file}")
                    print("\n✓ 重命名操作完成！")
                else:
                    print("✗ 重命名操作失败！")
            else:
                print("\n所有配置文件已经是数字格式，无需重命名")
        else:
            print("没有找到任何配置文件")
        return

    if args.name:
        # 命令行指定了配置文件
        print(f"\n使用命令行指定的配置文件: {args.name}")

        # 加载指定的批处理配置文件
        batch_data = get_batch_config(args.name)
        if batch_data is None:
            print("无法加载指定的批处理配置，程序退出")
            return

        batch_configs = batch_data.get("batch_configs", [])
        if not batch_configs:
            print("批处理配置文件中没有找到配置项，程序退出")
            return

        # 获取循环次数
        loop_count = batch_data.get("循环次数", 1)

        # 验证循环次数
        if not isinstance(loop_count, int) or loop_count < 1:
            print(f"警告：循环次数 {loop_count} 无效，必须是大于等于1的正整数，将使用默认值1")
            loop_count = 1

        if loop_count > 1:
            print(f"将运行 {loop_count} 轮批处理")

        # 依次执行每个批处理配置，收集结果
        batch_results = []
        for i, config in enumerate(batch_configs):
            result = run_single_batch_config(config, i, loop_count)
            batch_results.append(result)

        # 创建或更新带有准确率的配置文件副本
        create_config_copy_with_text_prompts(args.name, batch_data, batch_results)

        # 输出批处理执行总结
        print_batch_summary(batch_results)
        return

    # 交互式模式
    print("\n进入交互式模式")

    # 检查是否需要重命名文件
    available_configs = list_available_configs()
    if available_configs:
        print(f"\n当前可用的配置文件:")
        for i, config_file in enumerate(available_configs, 1):
            print(f"  {i}. {config_file}")

        # 检查是否有非数字命名的文件
        non_numeric_files = [f for f in available_configs if not f[:-5].isdigit()]
        if non_numeric_files:
            print(f"\n检测到 {len(non_numeric_files)} 个非数字命名的配置文件")
            rename_choice = input("是否要将所有配置文件重命名为数字格式？(y/n): ").strip().lower()
            if rename_choice in ['y', 'yes']:
                if rename_batch_config_files():
                    print("文件重命名完成，请重新运行程序")
                    return
                else:
                    print("文件重命名失败")

    # 第一步：选择输入模式
    input_mode = get_input_mode_choice()

    if input_mode == '2':
        # batch_configs读取模式
        print("\n您选择了batch_configs读取模式")

        # 加载批处理配置文件（按数字排序的最小文件）
        batch_data = get_batch_config()
        if batch_data is None:
            print("无法加载批处理配置，程序退出")
            return

        batch_configs = batch_data.get("batch_configs", [])
        if not batch_configs:
            print("批处理配置文件中没有找到配置项，程序退出")
            return

        # 获取循环次数
        loop_count = batch_data.get("循环次数", 1)

        # 验证循环次数
        if not isinstance(loop_count, int) or loop_count < 1:
            print(f"警告：循环次数 {loop_count} 无效，必须是大于等于1的正整数，将使用默认值1")
            loop_count = 1

        if loop_count > 1:
            print(f"将运行 {loop_count} 轮批处理")

        # 依次执行每个批处理配置，收集结果
        batch_results = []
        for i, config in enumerate(batch_configs):
            result = run_single_batch_config(config, i, loop_count)
            batch_results.append(result)

        # 创建或更新带有准确率的配置文件副本
        create_config_copy_with_text_prompts(None, batch_data, batch_results)

        # 输出批处理执行总结
        print_batch_summary(batch_results)
        return

    # 手动输入模式（原有逻辑）
    print("\n您选择了手动输入模式")

    # 第二步：选择处理模式
    processing_choice = get_processing_mode_choice()

    if processing_choice == '1':
        # 单阶段：直接调用one_stage_test脚本
        print("\n您选择了单阶段处理模式")
        success = run_script("one_stage_test.py")
        if success:
            print("\n✓ 单阶段处理完成！")
        else:
            print("\n✗ 单阶段处理失败！")

    elif processing_choice == '2':
        # 双阶段不发图片：先调用test再调用test2
        print("\n您选择了双阶段不发图片模式")

        # 询问round2批改模式
        round2_grading_choice = get_round2_grading_mode()
        grading_mode = 'model' if round2_grading_choice == '1' else 'json_compare'
        print(f"您选择了{'大模型批改' if round2_grading_choice == '1' else 'JSON比对'}模式")

        print("将依次运行：test.py -> test2.py")

        # 运行test.py并传递双阶段标志
        success1 = run_script("test.py", ["--stage", "dual"])
        if not success1:
            print("\n✗ test.py 运行失败，停止后续流程")
            cleanup_config()
            return

        # 加载test.py保存的配置
        config = load_config()
        if not config:
            print("\n✗ 无法加载配置信息，停止后续流程")
            return

        # 添加批改模式到配置中
        config['grading_mode'] = grading_mode

        # 重新保存配置
        config_file = save_config(config)

        # 运行test2.py并传递配置
        success2 = run_script("test2.py", ["--config", config_file])
        if success2:
            print("\n✓ 双阶段处理完成（不发图片）！")
        else:
            print("\n✗ test2.py 运行失败！")

        # 清理配置文件
        cleanup_config()

    elif processing_choice == '3':
        # 双阶段发图片：先调用test再调用test3
        print("\n您选择了双阶段发图片模式")
        print("将依次运行：test.py -> test3.py")

        # 运行test.py并传递双阶段标志
        success1 = run_script("test.py", ["--stage", "dual"])
        if not success1:
            print("\n✗ test.py 运行失败，停止后续流程")
            cleanup_config()
            return

        # 加载test.py保存的配置
        config = load_config()
        if not config:
            print("\n✗ 无法加载配置信息，停止后续流程")
            return

        # 运行test3.py并传递配置
        success3 = run_script("test3.py", ["--config", "temp_config.json"])
        if success3:
            print("\n✓ 双阶段处理完成（发图片）！")
        else:
            print("\n✗ test3.py 运行失败！")

        # 清理配置文件
        cleanup_config()
    
    print("\n程序运行结束。")

if __name__ == "__main__":
    # 设置日志记录
    log_file_path = setup_logging()

    print(f"日志将保存到: {log_file_path}")
    print("=" * 60)

    # 使用日志记录器运行main函数
    with Logger(log_file_path):
        try:
            main()
        except Exception as e:
            print(f"\n程序执行过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print(f"\n日志已保存到: {log_file_path}")
