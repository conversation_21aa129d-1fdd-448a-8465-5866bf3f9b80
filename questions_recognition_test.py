# export ARK_API_KEY="YOUR_API_KEY" 查看API KEY

import os
import json
import datetime
import re
import markdown
from bs4 import BeautifulSoup
import multiprocessing  # 新增：导入多进程模块
import time  # 新增：用于记录时间
from tqdm import tqdm  # 新增：导入进度条模块

from image_utils import image_to_base64

def generate_default_error_json(question_count=1, json_type="answer"):
    """
    生成默认的错误JSON格式
    question_count: 题目数量
    json_type: "answer" 表示答案格式，"boolean" 表示布尔格式
    """
    if json_type == "boolean":
        # 对于test2.py, test3.py, one_stage_test.py，返回false的布尔值
        return json.dumps({f"题目{i}": False for i in range(1, question_count + 1)}, ensure_ascii=False)
    else:
        # 对于test.py，返回"API请求失败"的答案格式
        return json.dumps({f"题目 {i}": "API请求失败" for i in range(1, question_count + 1)}, ensure_ascii=False)

def extract_question_count_from_json(json_str):
    """
    从JSON字符串中提取题目数量
    """
    if not json_str:
        return 1
    try:
        data = json.loads(json_str)
        if isinstance(data, dict):
            return len(data)
    except:
        pass
    return 1

def process_single_image_local(task):
    """
    处理单张图片的本地操作（增强、缩放、base64编码）
    """
    image_path = task['image_path']
    img_filename = task['img_filename']
    use_enhance = task['use_enhance']
    enhance_threshold = task['enhance_threshold']
    scale = task['scale']
    use_pixel_connection = task['use_pixel_connection']

    try:
        base64_image = image_to_base64(image_path, use_enhance=use_enhance,
                                     enhance_threshold=enhance_threshold,
                                     scale=scale, use_pixel_connection=use_pixel_connection)
        return {
            'success': True,
            'img_filename': img_filename,
            'base64_image': base64_image,
            'image_path': image_path
        }
    except Exception as e:
        return {
            'success': False,
            'img_filename': img_filename,
            'base64_image': None,
            'image_path': image_path,
            'error': str(e)
        }

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")

def get_response_format_choice(model_id):
    """根据模型ID判断是否支持jsonObject，如果支持则让用户选择response_format"""
    # 支持jsonObject的模型列表（模型1、2、3）
    json_object_supported_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-flash-250715",
        "doubao-1-5-thinking-vision-pro-250428"
    ]

    if model_id in json_object_supported_models:
        print("选择response_format：")
        print("1. text")
        print("2. json_object")

        while True:
            user_input = input("请输入选择（1-2）：").strip()
            if user_input == "1":
                return "text"
            elif user_input == "2":
                return "json_object"
            else:
                print("输入无效，请输入 1 或 2")
    else:
        return "text"  # 不支持jsonObject的模型默认使用text

def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def process_single_image_api(task):
    """
    只做API推理，输入为task字典（包含base64、图片名、prompt等）
    """
    from volcenginesdkarkruntime import Ark
    import os
    img_filename = task['img_filename']
    base64_image = task['base64_image']
    user_prompt = task['user_prompt']
    client_api_key = task['client_api_key']
    model_id = task.get('model_id', 'doubao-seed-1-6-flash-250715')  # 支持模型参数
    response_format = task.get('response_format', 'text')  # 获取response_format参数
    index = task['index']
    image_path_prefix = task['image_path_prefix']
    # 获取API参数，如果没有传入则使用默认值
    temperature = task.get('temperature', 1)
    top_p = task.get('top_p', 0.7)
    max_tokens_param = task.get('max_tokens')  # 如果没有传入则为None，后面会使用默认逻辑
    sep = f"\n{'=' * 50}\n"
    info = f"处理第 {index} 张图片: {img_filename}"
    current_image_output_lines = []
    current_image_output_lines.append(sep)
    current_image_output_lines.append(info + "\n")
    current_image_output_lines.append(sep)
    current_image_output_lines.append(f"![{img_filename}]({image_path_prefix}{img_filename})\n")

    # 重试机制：最多重试2次，只对Connection error进行重试
    max_retries = 2
    for attempt in range(max_retries + 1):
        try:
            client_local = Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key=client_api_key,
            )
            # 记录开始时间
            start_time = time.time()

            # 构建请求参数（先保存未加密版本用于显示）
            request_params_display = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}}
                        ]
                    }
                ],
                "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params_display["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params_display["top_p"] = top_p

            # 构建实际请求参数（包含加密头）
            request_params = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": user_prompt},
                            {"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}}
                        ]
                    }
                ],
                "extra_headers": {'x-is-encrypted': 'true'},
                "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params["top_p"] = top_p

            # 如果选择了json_object格式，添加response_format参数
            if response_format == "json_object":
                request_params["response_format"] = {"type": "json_object"}
                request_params_display["response_format"] = {"type": "json_object"}

            response = client_local.chat.completions.create(**request_params)
            # 记录结束时间并计算响应时间
            end_time = time.time()
            response_time = end_time - start_time
            resp_content = response.choices[0].message.content.strip()
            if resp_content.startswith("```json"):
                resp_content = resp_content[7:]
            if resp_content.startswith("```"):
                resp_content = resp_content[3:]
            if resp_content.endswith("```"):
                resp_content = resp_content[:-3]
            resp_content = resp_content.strip()

            # 如果是重试成功，添加重试信息
            if attempt > 0:
                current_image_output_lines.append(f"### 重试信息：第 {attempt + 1} 次尝试成功\n")

            current_image_output_lines.append(f"### 响应内容：\n")
            current_image_output_lines.append("```json\n")

            # 尝试格式化JSON以提高可读性
            try:
                # 尝试解析并重新格式化JSON
                parsed_json = json.loads(resp_content)
                formatted_json = json.dumps(parsed_json, ensure_ascii=False, indent=2)
                current_image_output_lines.append(f"{formatted_json}\n")
            except json.JSONDecodeError:
                # 如果不是有效的JSON，直接输出原内容
                current_image_output_lines.append(f"{resp_content}\n")

            current_image_output_lines.append("```\n")

            # 添加请求体（使用未加密版本，省略base64图片内容）
            request_params_for_display = json.loads(json.dumps(request_params_display))  # 深拷贝未加密版本
            # 省略base64图片内容，只保留前10个字符
            for message in request_params_for_display.get("messages", []):
                if "content" in message and isinstance(message["content"], list):
                    for content_item in message["content"]:
                        if content_item.get("type") == "image_url" and "image_url" in content_item:
                            url = content_item["image_url"].get("url", "")
                            if url.startswith("data:image/"):
                                # 找到base64数据部分
                                if "," in url:
                                    prefix, base64_data = url.split(",", 1)
                                    if len(base64_data) > 10:
                                        content_item["image_url"]["url"] = f"{prefix},{base64_data[:10]}...[省略{len(base64_data)-10}个字符]"

            current_image_output_lines.append(f"### 请求体：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{json.dumps(request_params_for_display, ensure_ascii=False, indent=2)}\n")
            current_image_output_lines.append("```\n")

            # 添加响应时间记录
            current_image_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")
            # token用量信息
            usage = getattr(response, 'usage', None)
            total_tokens = usage.total_tokens if usage and hasattr(usage, 'total_tokens') else None
            cached_tokens = None
            reasoning_tokens = None
            if usage:
                if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
                    cached_tokens = getattr(usage.prompt_tokens_details, 'cached_tokens', None)
                if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                    reasoning_tokens = getattr(usage.completion_tokens_details, 'reasoning_tokens', None)
            current_image_output_lines.append("### token用量\n")
            current_image_output_lines.append(f"- total_tokens: {total_tokens}\n")
            current_image_output_lines.append(f"- cached_tokens: {cached_tokens}\n")
            current_image_output_lines.append(f"- reasoning_tokens: {reasoning_tokens}\n")
            return {
                'success': True,
                'image_path': task['image_path'],
                'output_lines': current_image_output_lines,
                'response_content': resp_content
            }
        except Exception as e:
            error_str = str(e)
            # 检查是否为Connection error，如果是且还有重试机会，则继续重试
            if "Connection error" in error_str and attempt < max_retries:
                print(f"[PID {os.getpid()}] 处理图片 {img_filename} 时出现连接错误，正在重试... (第 {attempt + 1} 次尝试)")
                time.sleep(1)  # 等待1秒后重试
                continue

            # 如果不是Connection error或已达到最大重试次数，则返回失败
            err_msg = f"处理图片 {img_filename} 时出错: {error_str}"
            if attempt > 0:
                err_msg += f" (已重试 {attempt} 次)"
            # 只在失败时打印错误信息
            print(f"[PID {os.getpid()}] {err_msg}")
            current_image_output_lines.append(err_msg + "\n")

            # 生成简单的错误JSON格式
            error_json = json.dumps({"请求异常": error_str}, ensure_ascii=False, indent=2)
            current_image_output_lines.append(f"### 响应内容：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{error_json}\n")
            current_image_output_lines.append("```\n")

            return {
                'success': False,
                'image_path': task['image_path'],
                'output_lines': current_image_output_lines,
                'response_content': error_json
            }

def run_questions_recognition_test():
    """
    运行题目识别测试
    """

    # 获取用户选择的模型
    model_id = get_model_choice()

    # 获取用户选择的response_format
    response_format = get_response_format_choice(model_id)

    print(f"使用模型: {model_id}")
    print(f"使用response_format: {response_format}")

    # 新增：让用户选择是否采用"灰度阀门与像素增强"处理
    while True:
        enhance_input = input("是否采用'灰度阀门与像素增强'处理？(y/n)：").strip().lower()
        if enhance_input in ("y", "n"):
            use_enhance = (enhance_input == "y")
            break
        else:
            print("请输入 y 或 n")

    # 设置灰度阈值为默认值200
    gray_threshold = 200

    # 新增：如果选择了灰度阀门与像素增强，询问是否采用黑色像素粘连
    use_pixel_connection = False
    if use_enhance:
        while True:
            connection_input = input("是否采用'黑色像素粘连'处理？(y/n)：").strip().lower()
            if connection_input in ("y", "n"):
                use_pixel_connection = (connection_input == "y")
                break
            else:
                print("请输入 y 或 n")

    # 新增：让用户输入放大倍数
    while True:
        try:
            scale_input = input("请输入图片放大倍数（如1、2、4、6、8，默认1）：").strip()
            if scale_input == "":
                scale = 1.0
                break
            scale = float(scale_input)
            if scale <= 0:
                print("放大倍数必须为正数！")
                continue
            break
        except ValueError:
            print("请输入有效的数字倍数！")

    # 构建questions_recognition相关的路径
    question_dir = "questions_recognition"
    images_dir = os.path.join(question_dir, "images")
    response_dir = os.path.join(question_dir, "response")
    prompt_file = os.path.join(question_dir, "prompt.md")
    return_format_prompt_file = os.path.join(question_dir, "return_format_prompt.md")

    print(f"\n使用路径：")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{response_dir}")
    print(f"提示词文件：{prompt_file}")
    print(f"返回格式提示词文件：{return_format_prompt_file}")

    # 检查并创建必要的目录
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(response_dir, exist_ok=True)

    # 读取prompt.md文件
    if not os.path.exists(prompt_file):
        print(f"错误：提示词文件 {prompt_file} 不存在！")
        print(f"请创建 {prompt_file} 文件并写入提示词内容")
        exit()

    try:
        with open(prompt_file, 'r', encoding='utf-8') as f:
            markdown_prompt = f.read().strip()
        print(f"已从文件 {prompt_file} 读取提示词")
        # 将markdown格式转换为纯文本
        prompt_content = markdown_to_text(markdown_prompt)
        print("已将markdown格式转换为纯文本")
    except Exception as e:
        print(f"读取提示词文件时出错：{str(e)}")
        exit()

    if not prompt_content:
        print("错误：提示词文件为空！")
        exit()

    # 读取return_format_prompt.md文件
    if not os.path.exists(return_format_prompt_file):
        print(f"错误：返回格式提示词文件 {return_format_prompt_file} 不存在！")
        print(f"请创建 {return_format_prompt_file} 文件并写入返回格式提示词内容")
        exit()

    try:
        with open(return_format_prompt_file, 'r', encoding='utf-8') as f:
            return_format_markdown = f.read().strip()
        print(f"已从文件 {return_format_prompt_file} 读取返回格式提示词")
        # 将markdown格式转换为纯文本
        return_format_content = markdown_to_text(return_format_markdown)
        print("已将返回格式markdown转换为纯文本")
    except Exception as e:
        print(f"读取返回格式提示词文件时出错：{str(e)}")
        exit()

    if not return_format_content:
        print("错误：返回格式提示词文件为空！")
        exit()

    # 合并两个提示词
    user_prompt = prompt_content + "\n\n" + return_format_content

    # 获取images文件夹中的所有图片文件
    image_files = get_image_files(images_dir)

    # 生成 response 文件夹和文件名
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    response_file = os.path.join(response_dir, f"{now}.md")
    output_lines = []

    # 新增：如果 use_enhance 为 True，最顶部插入说明
    if use_enhance:
        if use_pixel_connection:
            output_lines.append("使用'灰度阀门与像素增强'处理（含黑色像素粘连）\n\n")
        else:
            output_lines.append("使用'灰度阀门与像素增强'处理（不含黑色像素粘连）\n\n")

    # 预留准确率位置（稍后会在文件开头插入）
    output_lines.append(f"# 运行时间: {now}\n\n")
    output_lines.append(f"## 使用模型ID: {model_id}\n\n")
    output_lines.append(f"## 使用图片文件夹: images\n\n")
    output_lines.append(f"## 图片放大倍数: {scale}\n\n")

    # 添加使用的提示词
    output_lines.append(f"## 使用的提示词\n\n")
    output_lines.append(f"{user_prompt}\n\n")

    if not image_files:
        msg1 = "images文件夹中没有找到图片文件！"
        msg2 = "支持的格式：.jpg, .jpeg, .png, .gif, .webp"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
        all_processed_results = []
    else:
        msg1 = f"找到 {len(image_files)} 张图片，开始逐个处理..."
        print(msg1)
        print(f"使用的提示词: {user_prompt}")
        output_lines.append(msg1 + "\n")
        print("\n--- 开始本地处理图片（增强/缩放/编码） ---\n")
        api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

        # 准备本地处理任务（使用用户选择的参数）
        local_tasks = []
        for i, image_path in enumerate(image_files):
            img_filename = os.path.basename(image_path)
            local_tasks.append({
                'image_path': image_path,
                'img_filename': img_filename,
                'use_enhance': use_enhance,
                'enhance_threshold': gray_threshold,
                'scale': scale,
                'use_pixel_connection': use_pixel_connection
            })

        # 使用多进程进行本地图片处理
        num_processes = os.cpu_count() if os.cpu_count() else 4
        print(f"正在使用 {num_processes} 个进程进行本地图片处理...")

        local_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 使用imap代替map以支持进度条
            for result in tqdm(pool.imap(process_single_image_local, local_tasks),
                             total=len(local_tasks), desc="本地处理", unit="张"):
                local_results.append(result)

        # 准备API推理任务，只包含成功处理的图片
        api_tasks = []
        for i, local_result in enumerate(local_results):
            if local_result['success']:
                api_tasks.append({
                    'img_filename': local_result['img_filename'],
                    'base64_image': local_result['base64_image'],
                    'user_prompt': user_prompt,
                    'client_api_key': api_key_from_client_init,
                    'model_id': model_id,  # 添加模型ID参数
                    'response_format': response_format,  # 添加response_format参数
                    'index': i + 1,
                    'image_path_prefix': f"../images/",
                    'image_path': local_result['image_path'],
                    'temperature': 1,  # 默认temperature
                    'top_p': 0.7,  # 默认top_p
                    'max_tokens': None  # 使用默认max_tokens
                })
            else:
                print(f"跳过处理失败的图片: {local_result['img_filename']}")

        print(f"本地处理完成: {len(api_tasks)}/{len(local_tasks)} 张图片成功处理")
        print(f"\n--- 开始并行API推理 ---\n")
        print(f"将使用 {num_processes} 个进程进行并行API推理。")

        # 使用进度条显示API推理进度
        all_processed_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # 使用imap代替map以支持进度条
            for result in tqdm(pool.imap(process_single_image_api, api_tasks),
                             total=len(api_tasks), desc="API推理", unit="张"):
                all_processed_results.append(result)
        print("\n--- 并行API推理完成，合并结果 ---\n")
        for result in all_processed_results:
            output_lines.extend(result['output_lines'])
        sep = f"\n{'=' * 50}\n"
        output_lines.append(sep)
        output_lines.append("所有图片处理完成！\n")
        output_lines.append(sep)
        print(sep)
        print("所有图片处理完成！")
        print(sep)

    # 写入新内容到新文件
    with open(response_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    print(f"结果已保存到：{response_file}")

    return True

if __name__ == "__main__":
    run_questions_recognition_test()
